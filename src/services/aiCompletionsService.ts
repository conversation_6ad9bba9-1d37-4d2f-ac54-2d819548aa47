// AI Chat Completions Service for LiteLLM API
// Chat Completions interfaces
export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface ChatCompletionRequest {
  model: string;
  messages: ChatMessage[];
  max_tokens?: number;
  temperature?: number;
  stream?: boolean;
}

export interface ChatCompletionChoice {
  finish_reason: string;
  index: number;
  message: ChatMessage;
  logprobs: null;
}

export interface CompletionUsage {
  completion_tokens: number;
  prompt_tokens: number;
  total_tokens: number;
  completion_tokens_details: {
    accepted_prediction_tokens: number;
    audio_tokens: number;
    reasoning_tokens: number;
    rejected_prediction_tokens: number;
  };
  prompt_tokens_details: {
    audio_tokens: number;
    cached_tokens: number;
  };
}

export interface ChatCompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: ChatCompletionChoice[];
  usage: CompletionUsage;
}

export class AICompletionsService {
  private readonly endpoint: string;
  private readonly authorization: string;

  constructor() {
    // In browser environment, only NEXT_PUBLIC_ variables are available
    this.endpoint = process.env.NEXT_PUBLIC_LITE_LLM_ENDPOINT || '';
    this.authorization = process.env.NEXT_PUBLIC_LITE_LLM_API_KEY || '';

    if (!this.endpoint) {
      throw new Error('NEXT_PUBLIC_LITE_LLM_ENDPOINT environment variable is required');
    }

    if (!this.authorization) {
      throw new Error('NEXT_PUBLIC_LITE_LLM_API_KEY environment variable is required');
    }
  }

  async createChatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    try {
      const response = await fetch(`${this.endpoint}/v1/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this.authorization}`,
        },
        body: JSON.stringify(request),
      });

      console.log('AI Chat Completions API response:', response);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`AI Chat Completions API error: ${response.status} - ${errorText}`);
      }

      const data: ChatCompletionResponse = await response.json();
      return data;
    } catch (error) {
      console.error('Error calling AI Chat Completions API:', error);
      throw error;
    }
  }

  // Convenience method for chat completions
  async generateChatText(
    messages: ChatMessage[],
    options: Partial<Omit<ChatCompletionRequest, 'messages'>> = {}
  ): Promise<string> {
    const request: ChatCompletionRequest = {
      model: options.model || 'openai/gpt-4o',
      messages,
      max_tokens: options.max_tokens || 100,
      temperature: options.temperature || 0.7,
    };

    const response = await this.createChatCompletion(request);
    return response.choices[0]?.message?.content || '';
  }

  // Example usage method
  async askCurrentDate(): Promise<string> {
    return this.generateChatText([
      { role: 'user', content: 'วันนี้วันที่เท่าไร' }
    ], {
      model: 'openai/gpt-4o',
      max_tokens: 50,
      temperature: 0.7,
    });
  }
}

// Export singleton instance
export const aiCompletionsService = new AICompletionsService();

// Export default for easier imports
export default aiCompletionsService;
